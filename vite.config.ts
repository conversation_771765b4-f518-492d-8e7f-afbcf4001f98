import react from "@vitejs/plugin-react";

import path from "path";
import {defineConfig, loadEnv} from "vite";

// https://vitejs.dev/config/
export default defineConfig(({mode}) => {
  const env = loadEnv(mode, process.cwd(), "");

  // Log thông tin môi trường khi build
  console.log(`🚀 Building for: ${mode.toUpperCase()}`);
  console.log(`📍 Base URL: ${env.VITE_BASE_URL}`);
  console.log(`🔧 Is Production: ${env.VITE_IS_PRODUCTION}`);

  return {
    plugins: [react()],
    server: {
      port: typeof env.VITE_PORT === "number" ? env.VITE_PORT : parseInt(env.VITE_PORT),
      proxy: {
        "/api": {
          target: env.VITE_BASE_URL,
          changeOrigin: true,
          secure: false, // <PERSON><PERSON>u backend sử dụng HTTPS tự ký
        },
      },
    },
    root: "./",
    base: "/", // 👈 Sử dụng absolute path cho SPA routing
    resolve: {
      alias: {
        "@R": path.resolve(__dirname, "./src/assets"),
        "@src": path.resolve(__dirname, "./src"),
        "@components": path.resolve(__dirname, "./src/components"),
        "@configs": path.resolve(__dirname, "./src/configs"),
        "@constants": path.resolve(__dirname, "./src/constants"),
        "@pages": path.resolve(__dirname, "./src/pages"),
        "@utils": path.resolve(__dirname, "./src/utils"),
        "@services": path.resolve(__dirname, "./src/services"),
        "@hooks": path.resolve(__dirname, "./src/hooks"),
        "@i18n": path.resolve(__dirname, "./src/configs/i18n"),
      },
    },
  };
});
